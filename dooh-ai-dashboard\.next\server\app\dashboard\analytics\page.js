/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/analytics/page";
exports.ids = ["app/dashboard/analytics/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fanalytics%2Fpage&page=%2Fdashboard%2Fanalytics%2Fpage&appPaths=%2Fdashboard%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fanalytics%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fanalytics%2Fpage&page=%2Fdashboard%2Fanalytics%2Fpage&appPaths=%2Fdashboard%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fanalytics%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/analytics/page.tsx */ \"(rsc)/./src/app/dashboard/analytics/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'analytics',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/analytics/page\",\n        pathname: \"/dashboard/analytics\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fanalytics%2Fpage&page=%2Fdashboard%2Fanalytics%2Fpage&appPaths=%2Fdashboard%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fanalytics%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/analytics/page.tsx */ \"(rsc)/./src/app/dashboard/analytics/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2FuYWx5dGljcyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFBDRFxcXFxEZXNrdG9wXFxcXGRvb2ggYWlcXFxcZG9vaC1haS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYW5hbHl0aWNzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUF5SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUENEXFxEZXNrdG9wXFxkb29oIGFpXFxkb29oLWFpLWRhc2hib2FyZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/analytics/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/analytics/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dooh ai\\dooh-ai-dashboard\\src\\app\\dashboard\\analytics\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dooh ai\\dooh-ai-dashboard\\src\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a572c756d6c4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDRFxcRGVza3RvcFxcZG9vaCBhaVxcZG9vaC1haS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE1NzJjNzU2ZDZjNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUENEXFxEZXNrdG9wXFxkb29oIGFpXFxkb29oLWFpLWRhc2hib2FyZFxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/analytics/page.tsx */ \"(ssr)/./src/app/dashboard/analytics/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2FuYWx5dGljcyU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFBDRFxcXFxEZXNrdG9wXFxcXGRvb2ggYWlcXFxcZG9vaC1haS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYW5hbHl0aWNzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Canalytics%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUF5SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/analytics/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/analytics/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_dashboard_analytics_chart__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/analytics-chart */ \"(ssr)/./src/components/dashboard/analytics-chart.tsx\");\n/* harmony import */ var _components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/stats-card */ \"(ssr)/./src/components/dashboard/stats-card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,MousePointer,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,MousePointer,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,MousePointer,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,MousePointer,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,MousePointer,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,MousePointer,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,MousePointer,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,DollarSign,Download,Eye,Filter,MousePointer,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Mock analytics data\nconst mockAnalyticsData = [\n    {\n        name: 'يناير',\n        impressions: 400000,\n        clicks: 5600,\n        conversions: 180,\n        cost: 8500,\n        revenue: 25200\n    },\n    {\n        name: 'فبراير',\n        impressions: 450000,\n        clicks: 6300,\n        conversions: 210,\n        cost: 9200,\n        revenue: 28800\n    },\n    {\n        name: 'مارس',\n        impressions: 520000,\n        clicks: 7200,\n        conversions: 245,\n        cost: 10800,\n        revenue: 32400\n    },\n    {\n        name: 'أبريل',\n        impressions: 480000,\n        clicks: 6800,\n        conversions: 220,\n        cost: 9800,\n        revenue: 30200\n    },\n    {\n        name: 'مايو',\n        impressions: 590000,\n        clicks: 8100,\n        conversions: 280,\n        cost: 11500,\n        revenue: 38500\n    },\n    {\n        name: 'يونيو',\n        impressions: 610000,\n        clicks: 8500,\n        conversions: 295,\n        cost: 12200,\n        revenue: 41800\n    }\n];\nconst platformPerformance = [\n    {\n        name: 'Google Ads',\n        impressions: 850000,\n        clicks: 12500,\n        conversions: 420,\n        cost: 18500,\n        revenue: 58800\n    },\n    {\n        name: 'Facebook Ads',\n        impressions: 720000,\n        clicks: 9800,\n        conversions: 315,\n        cost: 14200,\n        revenue: 45600\n    },\n    {\n        name: 'TikTok Ads',\n        impressions: 680000,\n        clicks: 11200,\n        conversions: 380,\n        cost: 16800,\n        revenue: 52200\n    },\n    {\n        name: 'Snapchat Ads',\n        impressions: 420000,\n        clicks: 6800,\n        conversions: 220,\n        cost: 9800,\n        revenue: 31500\n    },\n    {\n        name: 'YouTube Ads',\n        impressions: 950000,\n        clicks: 14200,\n        conversions: 485,\n        cost: 22500,\n        revenue: 68400\n    },\n    {\n        name: 'Instagram Ads',\n        impressions: 580000,\n        clicks: 8900,\n        conversions: 295,\n        cost: 13200,\n        revenue: 42800\n    }\n];\nconst conversionFunnel = [\n    {\n        stage: 'المشاهدات',\n        value: 4200000,\n        percentage: 100\n    },\n    {\n        stage: 'النقرات',\n        value: 71400,\n        percentage: 1.7\n    },\n    {\n        stage: 'زيارات الموقع',\n        value: 64260,\n        percentage: 90\n    },\n    {\n        stage: 'إضافة للسلة',\n        value: 12852,\n        percentage: 20\n    },\n    {\n        stage: 'بدء الدفع',\n        value: 6426,\n        percentage: 50\n    },\n    {\n        stage: 'إتمام الشراء',\n        value: 2115,\n        percentage: 33\n    }\n];\nconst topKeywords = [\n    {\n        keyword: 'منتجات صيفية',\n        impressions: 125000,\n        clicks: 2800,\n        ctr: 2.24,\n        cpc: 1.85\n    },\n    {\n        keyword: 'عروض خاصة',\n        impressions: 98000,\n        clicks: 2200,\n        ctr: 2.24,\n        cpc: 1.92\n    },\n    {\n        keyword: 'تسوق اونلاين',\n        impressions: 87000,\n        clicks: 1950,\n        ctr: 2.24,\n        cpc: 2.15\n    },\n    {\n        keyword: 'خصومات',\n        impressions: 76000,\n        clicks: 1680,\n        ctr: 2.21,\n        cpc: 1.78\n    },\n    {\n        keyword: 'توصيل مجاني',\n        impressions: 65000,\n        clicks: 1420,\n        ctr: 2.18,\n        cpc: 2.05\n    }\n];\nfunction AnalyticsPage() {\n    const [selectedPeriod, setSelectedPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('6months');\n    const [selectedMetric, setSelectedMetric] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const totalStats = {\n        totalImpressions: mockAnalyticsData.reduce((sum, item)=>sum + item.impressions, 0),\n        totalClicks: mockAnalyticsData.reduce((sum, item)=>sum + item.clicks, 0),\n        totalConversions: mockAnalyticsData.reduce((sum, item)=>sum + item.conversions, 0),\n        totalCost: mockAnalyticsData.reduce((sum, item)=>sum + item.cost, 0),\n        totalRevenue: mockAnalyticsData.reduce((sum, item)=>sum + item.revenue, 0)\n    };\n    const averageCTR = totalStats.totalClicks / totalStats.totalImpressions * 100;\n    const averageCPC = totalStats.totalCost / totalStats.totalClicks;\n    const roas = totalStats.totalRevenue / totalStats.totalCost;\n    const conversionRate = totalStats.totalConversions / totalStats.totalClicks * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"p-6 space-y-6 bg-slate-50 dark:bg-slate-900 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-slate-900 dark:text-white\",\n                                children: \"التحليلات المتقدمة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-700 dark:text-slate-300\",\n                                children: \"تحليل شامل لأداء حملاتك الإعلانية ومؤشرات الأداء الرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                className: \"border-slate-200 dark:border-slate-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تصفية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"تصدير التقرير\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-slate-600 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                children: \"الفترة الزمنية:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    {\n                                        value: '7days',\n                                        label: '7 أيام'\n                                    },\n                                    {\n                                        value: '30days',\n                                        label: '30 يوم'\n                                    },\n                                    {\n                                        value: '3months',\n                                        label: '3 أشهر'\n                                    },\n                                    {\n                                        value: '6months',\n                                        label: '6 أشهر'\n                                    },\n                                    {\n                                        value: '1year',\n                                        label: 'سنة'\n                                    }\n                                ].map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: selectedPeriod === period.value ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setSelectedPeriod(period.value),\n                                        className: selectedPeriod === period.value ? \"bg-blue-600 text-white\" : \"\",\n                                        children: period.label\n                                    }, period.value, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                        title: \"إجمالي المشاهدات\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatNumber)(totalStats.totalImpressions),\n                        change: 12.5,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                        title: \"إجمالي النقرات\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatNumber)(totalStats.totalClicks),\n                        change: 8.3,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                        title: \"معدل النقر (CTR)\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatPercentage)(averageCTR / 100),\n                        change: -2.1,\n                        changeType: \"decrease\",\n                        icon: _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                        title: \"تكلفة النقرة (CPC)\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(averageCPC),\n                        change: -5.2,\n                        changeType: \"decrease\",\n                        icon: _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_5__.StatsCard, {\n                        title: \"عائد الإنفاق الإعلاني\",\n                        value: `${roas.toFixed(2)}x`,\n                        change: 15.8,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_analytics_chart__WEBPACK_IMPORTED_MODULE_4__.AnalyticsChart, {\n                        title: \"أداء الحملات الشهري\",\n                        description: \"المشاهدات والنقرات والتحويلات خلال الأشهر الماضية\",\n                        data: mockAnalyticsData,\n                        type: \"line\",\n                        dataKey: \"clicks\",\n                        xAxisKey: \"name\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_analytics_chart__WEBPACK_IMPORTED_MODULE_4__.AnalyticsChart, {\n                        title: \"الإيرادات مقابل التكلفة\",\n                        description: \"مقارنة الإيرادات مع تكلفة الإعلانات\",\n                        data: mockAnalyticsData,\n                        type: \"bar\",\n                        dataKey: \"revenue\",\n                        xAxisKey: \"name\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-slate-900 dark:text-white\",\n                                children: \"أداء المنصات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                className: \"text-slate-600 dark:text-slate-300\",\n                                children: \"مقارنة أداء الحملات عبر المنصات المختلفة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: platformPerformance.map((platform, index)=>{\n                                const ctr = platform.clicks / platform.impressions * 100;\n                                const roas = platform.revenue / platform.cost;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-slate-900 dark:text-white\",\n                                                    children: platform.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 md:grid-cols-5 gap-4 mt-2 text-sm text-slate-600 dark:text-slate-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"المشاهدات:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatNumber)(platform.impressions)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"النقرات:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatNumber)(platform.clicks)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"CTR:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                ctr.toFixed(2),\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"التكلفة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(platform.cost)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"ROAS:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                roas.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: roas > 3 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 23\n                                            }, this) : roas > 2 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-5 w-5 text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_DollarSign_Download_Eye_Filter_MousePointer_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-slate-900 dark:text-white\",\n                                        children: \"قمع التحويل\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-slate-600 dark:text-slate-300\",\n                                        children: \"تتبع رحلة العميل من المشاهدة إلى الشراء\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: conversionFunnel.map((stage, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-blue-600 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                                            children: stage.stage\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatNumber)(stage.value)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-slate-500 dark:text-slate-400\",\n                                                            children: [\n                                                                stage.percentage,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-slate-900 dark:text-white\",\n                                        children: \"أفضل الكلمات المفتاحية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        className: \"text-slate-600 dark:text-slate-300\",\n                                        children: \"الكلمات المفتاحية الأكثر أداءً في حملاتك\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: topKeywords.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                            children: keyword.keyword\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mt-1 text-xs text-slate-600 dark:text-slate-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"المشاهدات: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatNumber)(keyword.impressions)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \"النقرات: \",\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatNumber)(keyword.clicks)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                                            children: [\n                                                                \"CTR: \",\n                                                                keyword.ctr,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-slate-600 dark:text-slate-300\",\n                                                            children: [\n                                                                \"CPC: \",\n                                                                (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(keyword.cpc)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/analytics/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/sidebar */ \"(ssr)/./src/components/dashboard/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-slate-50 dark:bg-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 lg:ml-64 overflow-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXdEO0FBRXpDLFNBQVNDLGdCQUFnQixFQUN0Q0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDSixrRUFBT0E7Ozs7OzBCQUNSLDhEQUFDRztnQkFBSUMsV0FBVTswQkFDWkY7Ozs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDRFxcRGVza3RvcFxcZG9vaCBhaVxcZG9vaC1haS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxkYXNoYm9hcmRcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNpZGViYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3NpZGViYXInXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIGJnLXNsYXRlLTUwIGRhcms6Ymctc2xhdGUtOTAwXCI+XG4gICAgICA8U2lkZWJhciAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbGc6bWwtNjQgb3ZlcmZsb3ctYXV0b1wiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNpZGViYXIiLCJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/analytics-chart.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/analytics-chart.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsChart: () => (/* binding */ AnalyticsChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/component/Cell.js\");\n/* __next_internal_client_entry_do_not_use__ AnalyticsChart auto */ \n\n\nconst defaultColors = [\n    '#3b82f6',\n    '#ef4444',\n    '#10b981',\n    '#f59e0b',\n    '#8b5cf6',\n    '#06b6d4'\n];\nfunction AnalyticsChart({ title, description, data, type, dataKey = 'value', xAxisKey = 'name', colors = defaultColors }) {\n    const renderChart = ()=>{\n        switch(type){\n            case 'line':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 300,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.LineChart, {\n                        data: data,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                                strokeDasharray: \"3 3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: xAxisKey\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Line, {\n                                type: \"monotone\",\n                                dataKey: dataKey,\n                                stroke: colors[0],\n                                strokeWidth: 2,\n                                dot: {\n                                    fill: colors[0]\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this);\n            case 'bar':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 300,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.BarChart, {\n                        data: data,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                                strokeDasharray: \"3 3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: xAxisKey\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Bar, {\n                                dataKey: dataKey,\n                                fill: colors[0]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this);\n            case 'pie':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 300,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Pie, {\n                                data: data,\n                                cx: \"50%\",\n                                cy: \"50%\",\n                                labelLine: false,\n                                label: ({ name, percent })=>`${name} ${(percent * 100).toFixed(0)}%`,\n                                outerRadius: 80,\n                                fill: \"#8884d8\",\n                                dataKey: dataKey,\n                                children: data.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Cell, {\n                                        fill: colors[index % colors.length]\n                                    }, `cell-${index}`, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                        className: \"text-slate-600 dark:text-slate-300\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: renderChart()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/analytics-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n// import { useAuth } from '@/components/auth/auth-guard'\n\nconst navigation = [\n    {\n        name: 'لوحة التحكم',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        current: false\n    },\n    {\n        name: 'العملاء',\n        href: '/dashboard/clients',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الحملات',\n        href: '/dashboard/campaigns',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التحليلات',\n        href: '/dashboard/analytics',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الذكاء الاصطناعي',\n        href: '/dashboard/ai-insights',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التقارير',\n        href: '/dashboard/reports',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التنبيهات',\n        href: '/dashboard/notifications',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الأتمتة',\n        href: '/dashboard/automation',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        current: false\n    }\n];\nconst adminNavigation = [\n    {\n        name: 'المستخدمون',\n        href: '/dashboard/users',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الإعدادات',\n        href: '/dashboard/settings',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        current: false\n    }\n];\nfunction Sidebar({ className }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // const { userProfile, signOut, isAdmin } = useAuth()\n    // Mock data for demo\n    const userProfile = {\n        name: 'مستخدم تجريبي',\n        email: '<EMAIL>'\n    };\n    const signOut = ()=>console.log('Sign out');\n    const isAdmin = true;\n    const allNavigation = isAdmin ? [\n        ...navigation,\n        ...adminNavigation\n    ] : navigation;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('lg:hidden', className),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"DOOH AI\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 28\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 56\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 lg:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black/20\",\n                                onClick: ()=>setSidebarOpen(false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed left-0 top-0 h-full w-64 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                                    navigation: allNavigation,\n                                    pathname: pathname,\n                                    userProfile: userProfile,\n                                    signOut: signOut,\n                                    onNavigate: ()=>setSidebarOpen(false)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0', className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-grow bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                        navigation: allNavigation,\n                        pathname: pathname,\n                        userProfile: userProfile,\n                        signOut: signOut\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction SidebarContent({ navigation, pathname, userProfile, signOut, onNavigate }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-16 px-4 border-b border-slate-200 dark:border-slate-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-blue-600 dark:text-blue-400\",\n                    children: \"DOOH AI Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-4 space-y-2\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: item.href,\n                        onClick: onNavigate,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors', isActive ? 'bg-blue-600 text-white' : 'text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: userProfile?.name?.charAt(0)?.toUpperCase() || 'U'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium truncate text-slate-900 dark:text-white\",\n                                        children: userProfile?.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-600 dark:text-slate-400 truncate\",\n                                        children: userProfile?.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: signOut,\n                        className: \"w-full justify-start text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            \"تسجيل الخروج\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/stats-card.tsx":
/*!*************************************************!*\
  !*** ./src/components/dashboard/stats-card.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCard: () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction StatsCard({ title, value, change, changeType, icon: Icon, description, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        className: \"text-sm font-medium text-slate-700 dark:text-slate-300\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"h-4 w-4 text-blue-600 dark:text-blue-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold text-slate-900 dark:text-white\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    change !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-xs text-slate-600 dark:text-slate-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center', changeType === 'increase' && 'text-green-600 dark:text-green-400', changeType === 'decrease' && 'text-red-600 dark:text-red-400'),\n                                children: [\n                                    changeType === 'increase' ? '↗' : '↘',\n                                    \" \",\n                                    Math.abs(change),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"من الشهر الماضي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-slate-600 dark:text-slate-400 mt-1\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/stats-card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:text-white dark:hover:bg-blue-700\",\n            destructive: \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:text-white dark:hover:bg-red-700\",\n            outline: \"border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700\",\n            secondary: \"bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white hover:bg-slate-200 dark:hover:bg-slate-600\",\n            ghost: \"text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700\",\n            link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 text-slate-900 dark:text-white shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight text-slate-900 dark:text-white\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-slate-600 dark:text-slate-300\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateCPC: () => (/* binding */ calculateCPC),\n/* harmony export */   calculateCPM: () => (/* binding */ calculateCPM),\n/* harmony export */   calculateCTR: () => (/* binding */ calculateCTR),\n/* harmony export */   calculateROAS: () => (/* binding */ calculateROAS),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: currency\n    }).format(amount);\n}\nfunction formatNumber(num) {\n    return new Intl.NumberFormat('en-US').format(num);\n}\nfunction formatPercentage(value) {\n    return `${(value * 100).toFixed(2)}%`;\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n}\nfunction formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\nfunction calculateCTR(clicks, impressions) {\n    if (impressions === 0) return 0;\n    return clicks / impressions * 100;\n}\nfunction calculateCPC(cost, clicks) {\n    if (clicks === 0) return 0;\n    return cost / clicks;\n}\nfunction calculateCPM(cost, impressions) {\n    if (impressions === 0) return 0;\n    return cost / impressions * 1000;\n}\nfunction calculateROAS(revenue, cost) {\n    if (cost === 0) return 0;\n    return revenue / cost;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/recharts","vendor-chunks/es-toolkit","vendor-chunks/d3-shape","vendor-chunks/d3-scale","vendor-chunks/d3-array","vendor-chunks/d3-format","vendor-chunks/d3-interpolate","vendor-chunks/d3-time","vendor-chunks/use-sync-external-store","vendor-chunks/d3-time-format","vendor-chunks/d3-color","vendor-chunks/victory-vendor","vendor-chunks/eventemitter3","vendor-chunks/tiny-invariant","vendor-chunks/reselect","vendor-chunks/redux","vendor-chunks/redux-thunk","vendor-chunks/react-redux","vendor-chunks/internmap","vendor-chunks/immer","vendor-chunks/decimal.js-light","vendor-chunks/d3-path","vendor-chunks/@reduxjs","vendor-chunks/react-is"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fanalytics%2Fpage&page=%2Fdashboard%2Fanalytics%2Fpage&appPaths=%2Fdashboard%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fanalytics%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();