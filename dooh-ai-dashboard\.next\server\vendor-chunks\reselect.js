"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/reselect";
exports.ids = ["vendor-chunks/reselect"];
exports.modules = {

/***/ "(ssr)/../node_modules/reselect/dist/reselect.mjs":
/*!**************************************************!*\
  !*** ../node_modules/reselect/dist/reselect.mjs ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSelector: () => (/* binding */ createSelector),\n/* harmony export */   createSelectorCreator: () => (/* binding */ createSelectorCreator),\n/* harmony export */   createStructuredSelector: () => (/* binding */ createStructuredSelector),\n/* harmony export */   lruMemoize: () => (/* binding */ lruMemoize),\n/* harmony export */   referenceEqualityCheck: () => (/* binding */ referenceEqualityCheck),\n/* harmony export */   setGlobalDevModeChecks: () => (/* binding */ setGlobalDevModeChecks),\n/* harmony export */   unstable_autotrackMemoize: () => (/* binding */ autotrackMemoize),\n/* harmony export */   weakMapMemoize: () => (/* binding */ weakMapMemoize)\n/* harmony export */ });\n// src/devModeChecks/identityFunctionCheck.ts\nvar runIdentityFunctionCheck = (resultFunc, inputSelectorsResults, outputSelectorResult) => {\n  if (inputSelectorsResults.length === 1 && inputSelectorsResults[0] === outputSelectorResult) {\n    let isInputSameAsOutput = false;\n    try {\n      const emptyObject = {};\n      if (resultFunc(emptyObject) === emptyObject)\n        isInputSameAsOutput = true;\n    } catch {\n    }\n    if (isInputSameAsOutput) {\n      let stack = void 0;\n      try {\n        throw new Error();\n      } catch (e) {\n        ;\n        ({ stack } = e);\n      }\n      console.warn(\n        \"The result function returned its own inputs without modification. e.g\\n`createSelector([state => state.todos], todos => todos)`\\nThis could lead to inefficient memoization and unnecessary re-renders.\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.\",\n        { stack }\n      );\n    }\n  }\n};\n\n// src/devModeChecks/inputStabilityCheck.ts\nvar runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs) => {\n  const { memoize, memoizeOptions } = options;\n  const { inputSelectorResults, inputSelectorResultsCopy } = inputSelectorResultsObject;\n  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions);\n  const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);\n  if (!areInputSelectorResultsEqual) {\n    let stack = void 0;\n    try {\n      throw new Error();\n    } catch (e) {\n      ;\n      ({ stack } = e);\n    }\n    console.warn(\n      \"An input selector returned a different result when passed same arguments.\\nThis means your output selector will likely run more frequently than intended.\\nAvoid returning a new reference inside your input selector, e.g.\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`\",\n      {\n        arguments: inputSelectorArgs,\n        firstInputs: inputSelectorResults,\n        secondInputs: inputSelectorResultsCopy,\n        stack\n      }\n    );\n  }\n};\n\n// src/devModeChecks/setGlobalDevModeChecks.ts\nvar globalDevModeChecks = {\n  inputStabilityCheck: \"once\",\n  identityFunctionCheck: \"once\"\n};\nvar setGlobalDevModeChecks = (devModeChecks) => {\n  Object.assign(globalDevModeChecks, devModeChecks);\n};\n\n// src/utils.ts\nvar NOT_FOUND = /* @__PURE__ */ Symbol(\"NOT_FOUND\");\nfunction assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {\n  if (typeof func !== \"function\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {\n  if (typeof object !== \"object\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {\n  if (!array.every((item) => typeof item === \"function\")) {\n    const itemTypes = array.map(\n      (item) => typeof item === \"function\" ? `function ${item.name || \"unnamed\"}()` : typeof item\n    ).join(\", \");\n    throw new TypeError(`${errorMessage}[${itemTypes}]`);\n  }\n}\nvar ensureIsArray = (item) => {\n  return Array.isArray(item) ? item : [item];\n};\nfunction getDependencies(createSelectorArgs) {\n  const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;\n  assertIsArrayOfFunctions(\n    dependencies,\n    `createSelector expects all input-selectors to be functions, but received the following types: `\n  );\n  return dependencies;\n}\nfunction collectInputSelectorResults(dependencies, inputSelectorArgs) {\n  const inputSelectorResults = [];\n  const { length } = dependencies;\n  for (let i = 0; i < length; i++) {\n    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));\n  }\n  return inputSelectorResults;\n}\nvar getDevModeChecksExecutionInfo = (firstRun, devModeChecks) => {\n  const { identityFunctionCheck, inputStabilityCheck } = {\n    ...globalDevModeChecks,\n    ...devModeChecks\n  };\n  return {\n    identityFunctionCheck: {\n      shouldRun: identityFunctionCheck === \"always\" || identityFunctionCheck === \"once\" && firstRun,\n      run: runIdentityFunctionCheck\n    },\n    inputStabilityCheck: {\n      shouldRun: inputStabilityCheck === \"always\" || inputStabilityCheck === \"once\" && firstRun,\n      run: runInputStabilityCheck\n    }\n  };\n};\n\n// src/autotrackMemoize/autotracking.ts\nvar $REVISION = 0;\nvar CURRENT_TRACKER = null;\nvar Cell = class {\n  revision = $REVISION;\n  _value;\n  _lastValue;\n  _isEqual = tripleEq;\n  constructor(initialValue, isEqual = tripleEq) {\n    this._value = this._lastValue = initialValue;\n    this._isEqual = isEqual;\n  }\n  // Whenever a storage value is read, it'll add itself to the current tracker if\n  // one exists, entangling its state with that cache.\n  get value() {\n    CURRENT_TRACKER?.add(this);\n    return this._value;\n  }\n  // Whenever a storage value is updated, we bump the global revision clock,\n  // assign the revision for this storage to the new value, _and_ we schedule a\n  // rerender. This is important, and it's what makes autotracking  _pull_\n  // based. We don't actively tell the caches which depend on the storage that\n  // anything has happened. Instead, we recompute the caches when needed.\n  set value(newValue) {\n    if (this.value === newValue)\n      return;\n    this._value = newValue;\n    this.revision = ++$REVISION;\n  }\n};\nfunction tripleEq(a, b) {\n  return a === b;\n}\nvar TrackingCache = class {\n  _cachedValue;\n  _cachedRevision = -1;\n  _deps = [];\n  hits = 0;\n  fn;\n  constructor(fn) {\n    this.fn = fn;\n  }\n  clear() {\n    this._cachedValue = void 0;\n    this._cachedRevision = -1;\n    this._deps = [];\n    this.hits = 0;\n  }\n  get value() {\n    if (this.revision > this._cachedRevision) {\n      const { fn } = this;\n      const currentTracker = /* @__PURE__ */ new Set();\n      const prevTracker = CURRENT_TRACKER;\n      CURRENT_TRACKER = currentTracker;\n      this._cachedValue = fn();\n      CURRENT_TRACKER = prevTracker;\n      this.hits++;\n      this._deps = Array.from(currentTracker);\n      this._cachedRevision = this.revision;\n    }\n    CURRENT_TRACKER?.add(this);\n    return this._cachedValue;\n  }\n  get revision() {\n    return Math.max(...this._deps.map((d) => d.revision), 0);\n  }\n};\nfunction getValue(cell) {\n  if (!(cell instanceof Cell)) {\n    console.warn(\"Not a valid cell! \", cell);\n  }\n  return cell.value;\n}\nfunction setValue(storage, value) {\n  if (!(storage instanceof Cell)) {\n    throw new TypeError(\n      \"setValue must be passed a tracked store created with `createStorage`.\"\n    );\n  }\n  storage.value = storage._lastValue = value;\n}\nfunction createCell(initialValue, isEqual = tripleEq) {\n  return new Cell(initialValue, isEqual);\n}\nfunction createCache(fn) {\n  assertIsFunction(\n    fn,\n    \"the first parameter to `createCache` must be a function\"\n  );\n  return new TrackingCache(fn);\n}\n\n// src/autotrackMemoize/tracking.ts\nvar neverEq = (a, b) => false;\nfunction createTag() {\n  return createCell(null, neverEq);\n}\nfunction dirtyTag(tag, value) {\n  setValue(tag, value);\n}\nvar consumeCollection = (node) => {\n  let tag = node.collectionTag;\n  if (tag === null) {\n    tag = node.collectionTag = createTag();\n  }\n  getValue(tag);\n};\nvar dirtyCollection = (node) => {\n  const tag = node.collectionTag;\n  if (tag !== null) {\n    dirtyTag(tag, null);\n  }\n};\n\n// src/autotrackMemoize/proxy.ts\nvar REDUX_PROXY_LABEL = Symbol();\nvar nextId = 0;\nvar proto = Object.getPrototypeOf({});\nvar ObjectTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy(this, objectProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar objectProxyHandler = {\n  get(node, key) {\n    function calculateResult() {\n      const { value } = node;\n      const childValue = Reflect.get(value, key);\n      if (typeof key === \"symbol\") {\n        return childValue;\n      }\n      if (key in proto) {\n        return childValue;\n      }\n      if (typeof childValue === \"object\" && childValue !== null) {\n        let childNode = node.children[key];\n        if (childNode === void 0) {\n          childNode = node.children[key] = createNode(childValue);\n        }\n        if (childNode.tag) {\n          getValue(childNode.tag);\n        }\n        return childNode.proxy;\n      } else {\n        let tag = node.tags[key];\n        if (tag === void 0) {\n          tag = node.tags[key] = createTag();\n          tag.value = childValue;\n        }\n        getValue(tag);\n        return childValue;\n      }\n    }\n    const res = calculateResult();\n    return res;\n  },\n  ownKeys(node) {\n    consumeCollection(node);\n    return Reflect.ownKeys(node.value);\n  },\n  getOwnPropertyDescriptor(node, prop) {\n    return Reflect.getOwnPropertyDescriptor(node.value, prop);\n  },\n  has(node, prop) {\n    return Reflect.has(node.value, prop);\n  }\n};\nvar ArrayTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy([this], arrayProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar arrayProxyHandler = {\n  get([node], key) {\n    if (key === \"length\") {\n      consumeCollection(node);\n    }\n    return objectProxyHandler.get(node, key);\n  },\n  ownKeys([node]) {\n    return objectProxyHandler.ownKeys(node);\n  },\n  getOwnPropertyDescriptor([node], prop) {\n    return objectProxyHandler.getOwnPropertyDescriptor(node, prop);\n  },\n  has([node], prop) {\n    return objectProxyHandler.has(node, prop);\n  }\n};\nfunction createNode(value) {\n  if (Array.isArray(value)) {\n    return new ArrayTreeNode(value);\n  }\n  return new ObjectTreeNode(value);\n}\nfunction updateNode(node, newValue) {\n  const { value, tags, children } = node;\n  node.value = newValue;\n  if (Array.isArray(value) && Array.isArray(newValue) && value.length !== newValue.length) {\n    dirtyCollection(node);\n  } else {\n    if (value !== newValue) {\n      let oldKeysSize = 0;\n      let newKeysSize = 0;\n      let anyKeysAdded = false;\n      for (const _key in value) {\n        oldKeysSize++;\n      }\n      for (const key in newValue) {\n        newKeysSize++;\n        if (!(key in value)) {\n          anyKeysAdded = true;\n          break;\n        }\n      }\n      const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize;\n      if (isDifferent) {\n        dirtyCollection(node);\n      }\n    }\n  }\n  for (const key in tags) {\n    const childValue = value[key];\n    const newChildValue = newValue[key];\n    if (childValue !== newChildValue) {\n      dirtyCollection(node);\n      dirtyTag(tags[key], newChildValue);\n    }\n    if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      delete tags[key];\n    }\n  }\n  for (const key in children) {\n    const childNode = children[key];\n    const newChildValue = newValue[key];\n    const childValue = childNode.value;\n    if (childValue === newChildValue) {\n      continue;\n    } else if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      updateNode(childNode, newChildValue);\n    } else {\n      deleteNode(childNode);\n      delete children[key];\n    }\n  }\n}\nfunction deleteNode(node) {\n  if (node.tag) {\n    dirtyTag(node.tag, null);\n  }\n  dirtyCollection(node);\n  for (const key in node.tags) {\n    dirtyTag(node.tags[key], null);\n  }\n  for (const key in node.children) {\n    deleteNode(node.children[key]);\n  }\n}\n\n// src/lruMemoize.ts\nfunction createSingletonCache(equals) {\n  let entry;\n  return {\n    get(key) {\n      if (entry && equals(entry.key, key)) {\n        return entry.value;\n      }\n      return NOT_FOUND;\n    },\n    put(key, value) {\n      entry = { key, value };\n    },\n    getEntries() {\n      return entry ? [entry] : [];\n    },\n    clear() {\n      entry = void 0;\n    }\n  };\n}\nfunction createLruCache(maxSize, equals) {\n  let entries = [];\n  function get(key) {\n    const cacheIndex = entries.findIndex((entry) => equals(key, entry.key));\n    if (cacheIndex > -1) {\n      const entry = entries[cacheIndex];\n      if (cacheIndex > 0) {\n        entries.splice(cacheIndex, 1);\n        entries.unshift(entry);\n      }\n      return entry.value;\n    }\n    return NOT_FOUND;\n  }\n  function put(key, value) {\n    if (get(key) === NOT_FOUND) {\n      entries.unshift({ key, value });\n      if (entries.length > maxSize) {\n        entries.pop();\n      }\n    }\n  }\n  function getEntries() {\n    return entries;\n  }\n  function clear() {\n    entries = [];\n  }\n  return { get, put, getEntries, clear };\n}\nvar referenceEqualityCheck = (a, b) => a === b;\nfunction createCacheKeyComparator(equalityCheck) {\n  return function areArgumentsShallowlyEqual(prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n      return false;\n    }\n    const { length } = prev;\n    for (let i = 0; i < length; i++) {\n      if (!equalityCheck(prev[i], next[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n}\nfunction lruMemoize(func, equalityCheckOrOptions) {\n  const providedOptions = typeof equalityCheckOrOptions === \"object\" ? equalityCheckOrOptions : { equalityCheck: equalityCheckOrOptions };\n  const {\n    equalityCheck = referenceEqualityCheck,\n    maxSize = 1,\n    resultEqualityCheck\n  } = providedOptions;\n  const comparator = createCacheKeyComparator(equalityCheck);\n  let resultsCount = 0;\n  const cache = maxSize <= 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);\n  function memoized() {\n    let value = cache.get(arguments);\n    if (value === NOT_FOUND) {\n      value = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const entries = cache.getEntries();\n        const matchingEntry = entries.find(\n          (entry) => resultEqualityCheck(entry.value, value)\n        );\n        if (matchingEntry) {\n          value = matchingEntry.value;\n          resultsCount !== 0 && resultsCount--;\n        }\n      }\n      cache.put(arguments, value);\n    }\n    return value;\n  }\n  memoized.clearCache = () => {\n    cache.clear();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/autotrackMemoize/autotrackMemoize.ts\nfunction autotrackMemoize(func) {\n  const node = createNode(\n    []\n  );\n  let lastArgs = null;\n  const shallowEqual = createCacheKeyComparator(referenceEqualityCheck);\n  const cache = createCache(() => {\n    const res = func.apply(null, node.proxy);\n    return res;\n  });\n  function memoized() {\n    if (!shallowEqual(lastArgs, arguments)) {\n      updateNode(node, arguments);\n      lastArgs = arguments;\n    }\n    return cache.value;\n  }\n  memoized.clearCache = () => {\n    return cache.clear();\n  };\n  return memoized;\n}\n\n// src/weakMapMemoize.ts\nvar StrongRef = class {\n  constructor(value) {\n    this.value = value;\n  }\n  deref() {\n    return this.value;\n  }\n};\nvar Ref = typeof WeakRef !== \"undefined\" ? WeakRef : StrongRef;\nvar UNTERMINATED = 0;\nvar TERMINATED = 1;\nfunction createCacheNode() {\n  return {\n    s: UNTERMINATED,\n    v: void 0,\n    o: null,\n    p: null\n  };\n}\nfunction weakMapMemoize(func, options = {}) {\n  let fnNode = createCacheNode();\n  const { resultEqualityCheck } = options;\n  let lastResult;\n  let resultsCount = 0;\n  function memoized() {\n    let cacheNode = fnNode;\n    const { length } = arguments;\n    for (let i = 0, l = length; i < l; i++) {\n      const arg = arguments[i];\n      if (typeof arg === \"function\" || typeof arg === \"object\" && arg !== null) {\n        let objectCache = cacheNode.o;\n        if (objectCache === null) {\n          cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();\n        }\n        const objectNode = objectCache.get(arg);\n        if (objectNode === void 0) {\n          cacheNode = createCacheNode();\n          objectCache.set(arg, cacheNode);\n        } else {\n          cacheNode = objectNode;\n        }\n      } else {\n        let primitiveCache = cacheNode.p;\n        if (primitiveCache === null) {\n          cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();\n        }\n        const primitiveNode = primitiveCache.get(arg);\n        if (primitiveNode === void 0) {\n          cacheNode = createCacheNode();\n          primitiveCache.set(arg, cacheNode);\n        } else {\n          cacheNode = primitiveNode;\n        }\n      }\n    }\n    const terminatedNode = cacheNode;\n    let result;\n    if (cacheNode.s === TERMINATED) {\n      result = cacheNode.v;\n    } else {\n      result = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const lastResultValue = lastResult?.deref?.() ?? lastResult;\n        if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {\n          result = lastResultValue;\n          resultsCount !== 0 && resultsCount--;\n        }\n        const needsWeakRef = typeof result === \"object\" && result !== null || typeof result === \"function\";\n        lastResult = needsWeakRef ? new Ref(result) : result;\n      }\n    }\n    terminatedNode.s = TERMINATED;\n    terminatedNode.v = result;\n    return result;\n  }\n  memoized.clearCache = () => {\n    fnNode = createCacheNode();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/createSelectorCreator.ts\nfunction createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {\n  const createSelectorCreatorOptions = typeof memoizeOrOptions === \"function\" ? {\n    memoize: memoizeOrOptions,\n    memoizeOptions: memoizeOptionsFromArgs\n  } : memoizeOrOptions;\n  const createSelector2 = (...createSelectorArgs) => {\n    let recomputations = 0;\n    let dependencyRecomputations = 0;\n    let lastResult;\n    let directlyPassedOptions = {};\n    let resultFunc = createSelectorArgs.pop();\n    if (typeof resultFunc === \"object\") {\n      directlyPassedOptions = resultFunc;\n      resultFunc = createSelectorArgs.pop();\n    }\n    assertIsFunction(\n      resultFunc,\n      `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`\n    );\n    const combinedOptions = {\n      ...createSelectorCreatorOptions,\n      ...directlyPassedOptions\n    };\n    const {\n      memoize,\n      memoizeOptions = [],\n      argsMemoize = weakMapMemoize,\n      argsMemoizeOptions = [],\n      devModeChecks = {}\n    } = combinedOptions;\n    const finalMemoizeOptions = ensureIsArray(memoizeOptions);\n    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);\n    const dependencies = getDependencies(createSelectorArgs);\n    const memoizedResultFunc = memoize(function recomputationWrapper() {\n      recomputations++;\n      return resultFunc.apply(\n        null,\n        arguments\n      );\n    }, ...finalMemoizeOptions);\n    let firstRun = true;\n    const selector = argsMemoize(function dependenciesChecker() {\n      dependencyRecomputations++;\n      const inputSelectorResults = collectInputSelectorResults(\n        dependencies,\n        arguments\n      );\n      lastResult = memoizedResultFunc.apply(null, inputSelectorResults);\n      if (true) {\n        const { identityFunctionCheck, inputStabilityCheck } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);\n        if (identityFunctionCheck.shouldRun) {\n          identityFunctionCheck.run(\n            resultFunc,\n            inputSelectorResults,\n            lastResult\n          );\n        }\n        if (inputStabilityCheck.shouldRun) {\n          const inputSelectorResultsCopy = collectInputSelectorResults(\n            dependencies,\n            arguments\n          );\n          inputStabilityCheck.run(\n            { inputSelectorResults, inputSelectorResultsCopy },\n            { memoize, memoizeOptions: finalMemoizeOptions },\n            arguments\n          );\n        }\n        if (firstRun)\n          firstRun = false;\n      }\n      return lastResult;\n    }, ...finalArgsMemoizeOptions);\n    return Object.assign(selector, {\n      resultFunc,\n      memoizedResultFunc,\n      dependencies,\n      dependencyRecomputations: () => dependencyRecomputations,\n      resetDependencyRecomputations: () => {\n        dependencyRecomputations = 0;\n      },\n      lastResult: () => lastResult,\n      recomputations: () => recomputations,\n      resetRecomputations: () => {\n        recomputations = 0;\n      },\n      memoize,\n      argsMemoize\n    });\n  };\n  Object.assign(createSelector2, {\n    withTypes: () => createSelector2\n  });\n  return createSelector2;\n}\nvar createSelector = /* @__PURE__ */ createSelectorCreator(weakMapMemoize);\n\n// src/createStructuredSelector.ts\nvar createStructuredSelector = Object.assign(\n  (inputSelectorsObject, selectorCreator = createSelector) => {\n    assertIsObject(\n      inputSelectorsObject,\n      `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`\n    );\n    const inputSelectorKeys = Object.keys(inputSelectorsObject);\n    const dependencies = inputSelectorKeys.map(\n      (key) => inputSelectorsObject[key]\n    );\n    const structuredSelector = selectorCreator(\n      dependencies,\n      (...inputSelectorResults) => {\n        return inputSelectorResults.reduce((composition, value, index) => {\n          composition[inputSelectorKeys[index]] = value;\n          return composition;\n        }, {});\n      }\n    );\n    return structuredSelector;\n  },\n  { withTypes: () => createStructuredSelector }\n);\n\n//# sourceMappingURL=reselect.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/reselect/dist/reselect.mjs\n");

/***/ })

};
;