/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUF5SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFBDRFxcXFxEZXNrdG9wXFxcXGRvb2ggYWlcXFxcZG9vaC1haS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUENEXFxEZXNrdG9wXFxkb29oIGFpXFxkb29oLWFpLWRhc2hib2FyZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dooh ai\\dooh-ai-dashboard\\src\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dooh ai\\dooh-ai-dashboard\\src\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a572c756d6c4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDRFxcRGVza3RvcFxcZG9vaCBhaVxcZG9vaC1haS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE1NzJjNzU2ZDZjNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUENEXFxEZXNrdG9wXFxkb29oIGFpXFxkb29oLWFpLWRhc2hib2FyZFxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDUENEJTVDJTVDRGVza3RvcCU1QyU1Q2Rvb2glMjBhaSU1QyU1Q2Rvb2gtYWktZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNQQ0QlNUMlNUNEZXNrdG9wJTVDJTVDZG9vaCUyMGFpJTVDJTVDZG9vaC1haS1kYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDUENEJTVDJTVDRGVza3RvcCU1QyU1Q2Rvb2glMjBhaSU1QyU1Q2Rvb2gtYWktZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQXNKO0FBQ3RKO0FBQ0EsME9BQXlKO0FBQ3pKO0FBQ0EsME9BQXlKO0FBQ3pKO0FBQ0Esb1JBQStLO0FBQy9LO0FBQ0Esd09BQXdKO0FBQ3hKO0FBQ0EsNFBBQW1LO0FBQ25LO0FBQ0Esa1FBQXNLO0FBQ3RLO0FBQ0Esc1FBQXVLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQQ0RcXFxcRGVza3RvcFxcXFxkb29oIGFpXFxcXGRvb2gtYWktZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFBDRFxcXFxEZXNrdG9wXFxcXGRvb2ggYWlcXFxcZG9vaC1haS1kYXNoYm9hcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQQ0RcXFxcRGVza3RvcFxcXFxkb29oIGFpXFxcXGRvb2gtYWktZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFBDRFxcXFxEZXNrdG9wXFxcXGRvb2ggYWlcXFxcZG9vaC1haS1kYXNoYm9hcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxQQ0RcXFxcRGVza3RvcFxcXFxkb29oIGFpXFxcXGRvb2gtYWktZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFBDRFxcXFxEZXNrdG9wXFxcXGRvb2ggYWlcXFxcZG9vaC1haS1kYXNoYm9hcmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUF5SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBdUgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXFBDRFxcXFxEZXNrdG9wXFxcXGRvb2ggYWlcXFxcZG9vaC1haS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/sidebar */ \"(ssr)/./src/components/dashboard/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-slate-50 dark:bg-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 lg:ml-64 overflow-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXdEO0FBRXpDLFNBQVNDLGdCQUFnQixFQUN0Q0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDSixrRUFBT0E7Ozs7OzBCQUNSLDhEQUFDRztnQkFBSUMsV0FBVTswQkFDWkY7Ozs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDRFxcRGVza3RvcFxcZG9vaCBhaVxcZG9vaC1haS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxkYXNoYm9hcmRcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNpZGViYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3NpZGViYXInXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIGJnLXNsYXRlLTUwIGRhcms6Ymctc2xhdGUtOTAwXCI+XG4gICAgICA8U2lkZWJhciAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbGc6bWwtNjQgb3ZlcmZsb3ctYXV0b1wiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNpZGViYXIiLCJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/stats-card */ \"(ssr)/./src/components/dashboard/stats-card.tsx\");\n/* harmony import */ var _components_dashboard_analytics_chart__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/analytics-chart */ \"(ssr)/./src/components/dashboard/analytics-chart.tsx\");\n/* harmony import */ var _components_dashboard_recent_campaigns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/recent-campaigns */ \"(ssr)/./src/components/dashboard/recent-campaigns.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Eye,MousePointer,Plus,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Eye,MousePointer,Plus,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Eye,MousePointer,Plus,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Eye,MousePointer,Plus,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Eye,MousePointer,Plus,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Eye,MousePointer,Plus,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Eye,MousePointer,Plus,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,DollarSign,Eye,MousePointer,Plus,Target,TrendingUp,Users!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/mouse-pointer.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n// Mock data - في التطبيق الحقيقي، ستأتي هذه البيانات من API\nconst mockStats = {\n    totalCampaigns: 24,\n    activeCampaigns: 18,\n    totalSpent: 45230.50,\n    totalRevenue: 89450.75,\n    totalImpressions: 2450000,\n    totalClicks: 34500,\n    totalConversions: 1250,\n    averageCTR: 1.41,\n    averageCPC: 1.31,\n    averageROAS: 1.98\n};\nconst mockCampaigns = [\n    {\n        id: '1',\n        name: 'حملة الصيف 2024',\n        platform: 'google_ads',\n        status: 'active',\n        budget: 5000,\n        spent: 3200,\n        impressions: 125000,\n        clicks: 1800,\n        conversions: 45,\n        revenue: 6750,\n        start_date: '2024-01-15',\n        end_date: '2024-02-15',\n        created_at: '2024-01-10T10:00:00Z',\n        updated_at: '2024-01-20T15:30:00Z',\n        user_id: 'user1'\n    },\n    {\n        id: '2',\n        name: 'إعلانات المنتجات الجديدة',\n        platform: 'facebook_ads',\n        status: 'active',\n        budget: 3000,\n        spent: 2100,\n        impressions: 89000,\n        clicks: 1200,\n        conversions: 32,\n        revenue: 4800,\n        start_date: '2024-01-20',\n        created_at: '2024-01-18T09:00:00Z',\n        updated_at: '2024-01-25T11:20:00Z',\n        user_id: 'user1'\n    }\n];\nconst mockChartData = [\n    {\n        name: 'يناير',\n        impressions: 400000,\n        clicks: 5600,\n        conversions: 180\n    },\n    {\n        name: 'فبراير',\n        impressions: 450000,\n        clicks: 6300,\n        conversions: 210\n    },\n    {\n        name: 'مارس',\n        impressions: 520000,\n        clicks: 7200,\n        conversions: 245\n    },\n    {\n        name: 'أبريل',\n        impressions: 480000,\n        clicks: 6800,\n        conversions: 220\n    },\n    {\n        name: 'مايو',\n        impressions: 590000,\n        clicks: 8100,\n        conversions: 280\n    },\n    {\n        name: 'يونيو',\n        impressions: 610000,\n        clicks: 8500,\n        conversions: 295\n    }\n];\nconst mockPlatformData = [\n    {\n        name: 'Google Ads',\n        value: 25,\n        color: '#4285f4'\n    },\n    {\n        name: 'Facebook Ads',\n        value: 20,\n        color: '#1877f2'\n    },\n    {\n        name: 'TikTok Ads',\n        value: 18,\n        color: '#000000'\n    },\n    {\n        name: 'Snapchat Ads',\n        value: 15,\n        color: '#fffc00'\n    },\n    {\n        name: 'YouTube Ads',\n        value: 12,\n        color: '#ff0000'\n    },\n    {\n        name: 'Instagram Ads',\n        value: 10,\n        color: '#e4405f'\n    }\n];\nfunction DashboardPage() {\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockStats);\n    const [campaigns, setCampaigns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mockCampaigns);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            // في التطبيق الحقيقي، ستقوم بجلب البيانات من API هنا\n            setLoading(false);\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"p-6 space-y-6 bg-slate-50 dark:bg-slate-900 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-slate-900 dark:text-white\",\n                                children: \"لوحة التحكم\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-700 dark:text-slate-300\",\n                                children: \"نظرة عامة على أداء حملات عملائك الإعلانية عبر جميع المنصات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                className: \"border-slate-200 dark:border-slate-700\",\n                                onClick: ()=>window.location.href = '/dashboard/clients',\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"إضافة عميل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                onClick: ()=>window.location.href = '/dashboard/campaigns/new',\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"إنشاء حملة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                        title: \"إجمالي العملاء\",\n                        value: \"24\",\n                        change: 12,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        description: \"21 عميل نشط\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                        title: \"إجمالي الحملات\",\n                        value: stats.totalCampaigns,\n                        change: 12,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        description: `${stats.activeCampaigns} حملة نشطة`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                        title: \"إجمالي المصروفات\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(stats.totalSpent),\n                        change: 8.5,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                        title: \"إجمالي الإيرادات\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(stats.totalRevenue),\n                        change: 15.2,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                        title: \"متوسط ROAS\",\n                        value: \"2.37x\",\n                        change: 5.8,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                        title: \"إجمالي المشاهدات\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatNumber)(stats.totalImpressions),\n                        change: 5.8,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                        title: \"إجمالي النقرات\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatNumber)(stats.totalClicks),\n                        change: 7.2,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_stats_card__WEBPACK_IMPORTED_MODULE_4__.StatsCard, {\n                        title: \"معدل النقر (CTR)\",\n                        value: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatPercentage)(stats.averageCTR / 100),\n                        change: 3.1,\n                        changeType: \"increase\",\n                        icon: _barrel_optimize_names_BarChart3_DollarSign_Eye_MousePointer_Plus_Target_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_analytics_chart__WEBPACK_IMPORTED_MODULE_5__.AnalyticsChart, {\n                        title: \"أداء الحملات الشهري\",\n                        description: \"المشاهدات والنقرات والتحويلات خلال الأشهر الماضية\",\n                        data: mockChartData,\n                        type: \"line\",\n                        dataKey: \"clicks\",\n                        xAxisKey: \"name\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_analytics_chart__WEBPACK_IMPORTED_MODULE_5__.AnalyticsChart, {\n                        title: \"توزيع المنصات\",\n                        description: \"نسبة الإنفاق على كل منصة إعلانية\",\n                        data: mockPlatformData,\n                        type: \"pie\",\n                        dataKey: \"value\",\n                        xAxisKey: \"name\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 15\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"text-slate-900 dark:text-white\",\n                                children: \"أهم العملاء\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                className: \"text-slate-600 dark:text-slate-300\",\n                                children: \"العملاء الأكثر إنفاقاً وأداءً هذا الشهر\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                {\n                                    name: 'شركة التقنية المتقدمة',\n                                    spend: 125000,\n                                    revenue: 295000,\n                                    roas: 2.36,\n                                    campaigns: 5\n                                },\n                                {\n                                    name: 'متجر الأزياء العصرية',\n                                    spend: 85000,\n                                    revenue: 201000,\n                                    roas: 2.36,\n                                    campaigns: 4\n                                },\n                                {\n                                    name: 'مطاعم الذواقة',\n                                    spend: 25000,\n                                    revenue: 62000,\n                                    roas: 2.48,\n                                    campaigns: 2\n                                }\n                            ].map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: client.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                            children: client.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                            children: [\n                                                                client.campaigns,\n                                                                \" حملة نشطة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-4 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-slate-500 dark:text-slate-400\",\n                                                            children: \"الإنفاق\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold text-slate-900 dark:text-white\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(client.spend)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-slate-500 dark:text-slate-400\",\n                                                            children: \"الإيرادات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold text-slate-900 dark:text-white\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.formatCurrency)(client.revenue)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-slate-500 dark:text-slate-400\",\n                                                            children: \"ROAS\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-bold text-slate-900 dark:text-white\",\n                                                            children: [\n                                                                client.roas.toFixed(2),\n                                                                \"x\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recent_campaigns__WEBPACK_IMPORTED_MODULE_6__.RecentCampaigns, {\n                campaigns: campaigns,\n                onView: (campaign)=>console.log('View campaign:', campaign),\n                onEdit: (campaign)=>console.log('Edit campaign:', campaign),\n                onDelete: (campaign)=>console.log('Delete campaign:', campaign)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/analytics-chart.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/analytics-chart.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsChart: () => (/* binding */ AnalyticsChart)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../node_modules/recharts/es6/component/Cell.js\");\n/* __next_internal_client_entry_do_not_use__ AnalyticsChart auto */ \n\n\nconst defaultColors = [\n    '#3b82f6',\n    '#ef4444',\n    '#10b981',\n    '#f59e0b',\n    '#8b5cf6',\n    '#06b6d4'\n];\nfunction AnalyticsChart({ title, description, data, type, dataKey = 'value', xAxisKey = 'name', colors = defaultColors }) {\n    const renderChart = ()=>{\n        switch(type){\n            case 'line':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 300,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.LineChart, {\n                        data: data,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                                strokeDasharray: \"3 3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: xAxisKey\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Line, {\n                                type: \"monotone\",\n                                dataKey: dataKey,\n                                stroke: colors[0],\n                                strokeWidth: 2,\n                                dot: {\n                                    fill: colors[0]\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this);\n            case 'bar':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 300,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.BarChart, {\n                        data: data,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                                strokeDasharray: \"3 3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: xAxisKey\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Bar, {\n                                dataKey: dataKey,\n                                fill: colors[0]\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 11\n                }, this);\n            case 'pie':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 300,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.PieChart, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Pie, {\n                                data: data,\n                                cx: \"50%\",\n                                cy: \"50%\",\n                                labelLine: false,\n                                label: ({ name, percent })=>`${name} ${(percent * 100).toFixed(0)}%`,\n                                outerRadius: 80,\n                                fill: \"#8884d8\",\n                                dataKey: dataKey,\n                                children: data.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Cell, {\n                                        fill: colors[index % colors.length]\n                                    }, `cell-${index}`, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                        className: \"text-slate-600 dark:text-slate-300\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: renderChart()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\analytics-chart.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/analytics-chart.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/recent-campaigns.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/recent-campaigns.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentCampaigns: () => (/* binding */ RecentCampaigns)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Trash2!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Trash2!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Trash2!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ RecentCampaigns auto */ \n\n\n\n\n\nconst platformColors = {\n    google_ads: 'bg-blue-100 text-blue-800',\n    facebook_ads: 'bg-blue-100 text-blue-800',\n    instagram_ads: 'bg-pink-100 text-pink-800',\n    twitter_ads: 'bg-sky-100 text-sky-800',\n    tiktok_ads: 'bg-gray-100 text-gray-800',\n    snapchat_ads: 'bg-yellow-100 text-yellow-800',\n    youtube_ads: 'bg-red-100 text-red-800'\n};\nconst statusColors = {\n    active: 'bg-green-100 text-green-800',\n    paused: 'bg-yellow-100 text-yellow-800',\n    completed: 'bg-gray-100 text-gray-800',\n    draft: 'bg-gray-100 text-gray-800'\n};\nconst platformNames = {\n    google_ads: 'Google Ads',\n    facebook_ads: 'Facebook Ads',\n    instagram_ads: 'Instagram Ads',\n    twitter_ads: 'Twitter Ads',\n    tiktok_ads: 'TikTok Ads',\n    snapchat_ads: 'Snapchat Ads',\n    youtube_ads: 'YouTube Ads'\n};\nconst statusNames = {\n    active: 'نشط',\n    paused: 'متوقف',\n    completed: 'مكتمل',\n    draft: 'مسودة'\n};\nfunction RecentCampaigns({ campaigns, onView, onEdit, onDelete }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        className: \"text-slate-900 dark:text-white\",\n                        children: \"الحملات الأخيرة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                        className: \"text-slate-600 dark:text-slate-300\",\n                        children: \"آخر الحملات الإعلانية التي تم إنشاؤها أو تحديثها\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: campaigns.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8 text-slate-500 dark:text-slate-400\",\n                        children: \"لا توجد حملات حتى الآن\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 13\n                    }, this) : campaigns.map((campaign)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-slate-900 dark:text-white\",\n                                                    children: campaign.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                    className: platformColors[campaign.platform],\n                                                    children: platformNames[campaign.platform]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                    className: statusColors[campaign.status],\n                                                    children: statusNames[campaign.status]\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-slate-600 dark:text-slate-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"الميزانية:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(campaign.budget)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"المصروف:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatCurrency)(campaign.spent)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"النقرات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(campaign.clicks)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"التحويلات:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \" \",\n                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.formatNumber)(campaign.conversions)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        onView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>onView(campaign),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 21\n                                        }, this),\n                                        onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>onEdit(campaign),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 21\n                                        }, this),\n                                        onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: ()=>onDelete(campaign),\n                                            className: \"text-destructive hover:text-destructive\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, campaign.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\recent-campaigns.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/recent-campaigns.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n// import { useAuth } from '@/components/auth/auth-guard'\n\nconst navigation = [\n    {\n        name: 'لوحة التحكم',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        current: false\n    },\n    {\n        name: 'العملاء',\n        href: '/dashboard/clients',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الحملات',\n        href: '/dashboard/campaigns',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التحليلات',\n        href: '/dashboard/analytics',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الذكاء الاصطناعي',\n        href: '/dashboard/ai-insights',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التقارير',\n        href: '/dashboard/reports',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التنبيهات',\n        href: '/dashboard/notifications',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الأتمتة',\n        href: '/dashboard/automation',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        current: false\n    }\n];\nconst adminNavigation = [\n    {\n        name: 'المستخدمون',\n        href: '/dashboard/users',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الإعدادات',\n        href: '/dashboard/settings',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        current: false\n    }\n];\nfunction Sidebar({ className }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // const { userProfile, signOut, isAdmin } = useAuth()\n    // Mock data for demo\n    const userProfile = {\n        name: 'مستخدم تجريبي',\n        email: '<EMAIL>'\n    };\n    const signOut = ()=>console.log('Sign out');\n    const isAdmin = true;\n    const allNavigation = isAdmin ? [\n        ...navigation,\n        ...adminNavigation\n    ] : navigation;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('lg:hidden', className),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"DOOH AI\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 28\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 56\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 lg:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black/20\",\n                                onClick: ()=>setSidebarOpen(false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed left-0 top-0 h-full w-64 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                                    navigation: allNavigation,\n                                    pathname: pathname,\n                                    userProfile: userProfile,\n                                    signOut: signOut,\n                                    onNavigate: ()=>setSidebarOpen(false)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0', className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-grow bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                        navigation: allNavigation,\n                        pathname: pathname,\n                        userProfile: userProfile,\n                        signOut: signOut\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction SidebarContent({ navigation, pathname, userProfile, signOut, onNavigate }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-16 px-4 border-b border-slate-200 dark:border-slate-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-blue-600 dark:text-blue-400\",\n                    children: \"DOOH AI Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-4 space-y-2\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: item.href,\n                        onClick: onNavigate,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors', isActive ? 'bg-blue-600 text-white' : 'text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: userProfile?.name?.charAt(0)?.toUpperCase() || 'U'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium truncate text-slate-900 dark:text-white\",\n                                        children: userProfile?.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-600 dark:text-slate-400 truncate\",\n                                        children: userProfile?.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: signOut,\n                        className: \"w-full justify-start text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            \"تسجيل الخروج\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/stats-card.tsx":
/*!*************************************************!*\
  !*** ./src/components/dashboard/stats-card.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StatsCard: () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nfunction StatsCard({ title, value, change, changeType, icon: Icon, description, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        className: \"text-sm font-medium text-slate-700 dark:text-slate-300\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"h-4 w-4 text-blue-600 dark:text-blue-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-2xl font-bold text-slate-900 dark:text-white\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    change !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-xs text-slate-600 dark:text-slate-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)('flex items-center', changeType === 'increase' && 'text-green-600 dark:text-green-400', changeType === 'decrease' && 'text-red-600 dark:text-red-400'),\n                                children: [\n                                    changeType === 'increase' ? '↗' : '↘',\n                                    \" \",\n                                    Math.abs(change),\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"من الشهر الماضي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-slate-600 dark:text-slate-400 mt-1\",\n                        children: description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\stats-card.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/stats-card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800\",\n            secondary: \"border-transparent bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-600\",\n            destructive: \"border-transparent bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-800\",\n            outline: \"border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:text-white dark:hover:bg-blue-700\",\n            destructive: \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:text-white dark:hover:bg-red-700\",\n            outline: \"border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700\",\n            secondary: \"bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white hover:bg-slate-200 dark:hover:bg-slate-600\",\n            ghost: \"text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700\",\n            link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 text-slate-900 dark:text-white shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight text-slate-900 dark:text-white\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-slate-600 dark:text-slate-300\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFFRTtBQUVoQyxNQUFNRSxxQkFBT0YsNkNBQWdCLENBRzNCLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCxnSUFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkgsS0FBS00sV0FBVyxHQUFHO0FBRW5CLE1BQU1DLDJCQUFhVCw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JJLFdBQVdELFdBQVcsR0FBRztBQUV6QixNQUFNRSwwQkFBWVYsNkNBQWdCLENBR2hDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDSztRQUNDTCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCxxRkFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkssVUFBVUYsV0FBVyxHQUFHO0FBRXhCLE1BQU1JLGdDQUFrQlosNkNBQWdCLENBR3RDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDTztRQUNDUCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FBQyw4Q0FBOENHO1FBQzNELEdBQUdDLEtBQUs7Ozs7OztBQUdiTyxnQkFBZ0JKLFdBQVcsR0FBRztBQUU5QixNQUFNTSw0QkFBY2QsNkNBQWdCLENBR2xDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUFJRCxLQUFLQTtRQUFLRixXQUFXSCw4Q0FBRUEsQ0FBQyxZQUFZRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUVoRVMsWUFBWU4sV0FBVyxHQUFHO0FBRTFCLE1BQU1PLDJCQUFhZiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLDhCQUE4Qkc7UUFDM0MsR0FBR0MsS0FBSzs7Ozs7O0FBR2JVLFdBQVdQLFdBQVcsR0FBRztBQUV1RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQ0RcXERlc2t0b3BcXGRvb2ggYWlcXGRvb2gtYWktZGFzaGJvYXJkXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxjYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IENhcmQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXZcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJyb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc2xhdGUtMjAwIGRhcms6Ym9yZGVyLXNsYXRlLTcwMCBiZy13aGl0ZSBkYXJrOmJnLXNsYXRlLTgwMCB0ZXh0LXNsYXRlLTkwMCBkYXJrOnRleHQtd2hpdGUgc2hhZG93LXNtXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkLmRpc3BsYXlOYW1lID0gXCJDYXJkXCJcblxuY29uc3QgQ2FyZEhlYWRlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJmbGV4IGZsZXgtY29sIHNwYWNlLXktMS41IHAtNlwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkSGVhZGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkSGVhZGVyXCJcblxuY29uc3QgQ2FyZFRpdGxlID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTFBhcmFncmFwaEVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxIZWFkaW5nRWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGgzXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCBsZWFkaW5nLW5vbmUgdHJhY2tpbmctdGlnaHQgdGV4dC1zbGF0ZS05MDAgZGFyazp0ZXh0LXdoaXRlXCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkVGl0bGUuZGlzcGxheU5hbWUgPSBcIkNhcmRUaXRsZVwiXG5cbmNvbnN0IENhcmREZXNjcmlwdGlvbiA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxQYXJhZ3JhcGhFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MUGFyYWdyYXBoRWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPHBcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFwidGV4dC1zbSB0ZXh0LXNsYXRlLTYwMCBkYXJrOnRleHQtc2xhdGUtMzAwXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmREZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IFwiQ2FyZERlc2NyaXB0aW9uXCJcblxuY29uc3QgQ2FyZENvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MRGl2RWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxkaXYgcmVmPXtyZWZ9IGNsYXNzTmFtZT17Y24oXCJwLTYgcHQtMFwiLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9IC8+XG4pKVxuQ2FyZENvbnRlbnQuZGlzcGxheU5hbWUgPSBcIkNhcmRDb250ZW50XCJcblxuY29uc3QgQ2FyZEZvb3RlciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdlxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXCJmbGV4IGl0ZW1zLWNlbnRlciBwLTYgcHQtMFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5DYXJkRm9vdGVyLmRpc3BsYXlOYW1lID0gXCJDYXJkRm9vdGVyXCJcblxuZXhwb3J0IHsgQ2FyZCwgQ2FyZEhlYWRlciwgQ2FyZEZvb3RlciwgQ2FyZFRpdGxlLCBDYXJkRGVzY3JpcHRpb24sIENhcmRDb250ZW50IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiQ2FyZCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsImRpdiIsImRpc3BsYXlOYW1lIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsImgzIiwiQ2FyZERlc2NyaXB0aW9uIiwicCIsIkNhcmRDb250ZW50IiwiQ2FyZEZvb3RlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateCPC: () => (/* binding */ calculateCPC),\n/* harmony export */   calculateCPM: () => (/* binding */ calculateCPM),\n/* harmony export */   calculateCTR: () => (/* binding */ calculateCTR),\n/* harmony export */   calculateROAS: () => (/* binding */ calculateROAS),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: currency\n    }).format(amount);\n}\nfunction formatNumber(num) {\n    return new Intl.NumberFormat('en-US').format(num);\n}\nfunction formatPercentage(value) {\n    return `${(value * 100).toFixed(2)}%`;\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n}\nfunction formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\nfunction calculateCTR(clicks, impressions) {\n    if (impressions === 0) return 0;\n    return clicks / impressions * 100;\n}\nfunction calculateCPC(cost, clicks) {\n    if (clicks === 0) return 0;\n    return cost / clicks;\n}\nfunction calculateCPM(cost, impressions) {\n    if (impressions === 0) return 0;\n    return cost / impressions * 1000;\n}\nfunction calculateROAS(revenue, cost) {\n    if (cost === 0) return 0;\n    return revenue / cost;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/recharts","vendor-chunks/@reduxjs","vendor-chunks/d3-shape","vendor-chunks/es-toolkit","vendor-chunks/decimal.js-light","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/d3-scale","vendor-chunks/reselect","vendor-chunks/d3-time-format","vendor-chunks/redux","vendor-chunks/d3-time","vendor-chunks/d3-format","vendor-chunks/d3-array","vendor-chunks/d3-color","vendor-chunks/use-sync-external-store","vendor-chunks/eventemitter3","vendor-chunks/d3-interpolate","vendor-chunks/react-is","vendor-chunks/d3-path","vendor-chunks/internmap","vendor-chunks/victory-vendor","vendor-chunks/tiny-invariant","vendor-chunks/redux-thunk"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();