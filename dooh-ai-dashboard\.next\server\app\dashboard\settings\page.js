/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/settings/page";
exports.ids = ["app/dashboard/settings/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fsettings%2Fpage&page=%2Fdashboard%2Fsettings%2Fpage&appPaths=%2Fdashboard%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fsettings%2Fpage&page=%2Fdashboard%2Fsettings%2Fpage&appPaths=%2Fdashboard%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/settings/page.tsx */ \"(rsc)/./src/app/dashboard/settings/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'settings',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/settings/page\",\n        pathname: \"/dashboard/settings\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fsettings%2Fpage&page=%2Fdashboard%2Fsettings%2Fpage&appPaths=%2Fdashboard%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(rsc)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUF5SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/settings/page.tsx */ \"(rsc)/./src/app/dashboard/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3NldHRpbmdzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUFpSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxzZXR0aW5nc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUENEXFxEZXNrdG9wXFxkb29oIGFpXFxkb29oLWFpLWRhc2hib2FyZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dooh ai\\dooh-ai-dashboard\\src\\app\\dashboard\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/settings/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/settings/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\dooh ai\\dooh-ai-dashboard\\src\\app\\dashboard\\settings\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a572c756d6c4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDRFxcRGVza3RvcFxcZG9vaCBhaVxcZG9vaC1haS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE1NzJjNzU2ZDZjNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUENEXFxEZXNrdG9wXFxkb29oIGFpXFxkb29oLWFpLWRhc2hib2FyZFxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XG5pbXBvcnQgeyBHZWlzdCwgR2Vpc3RfTW9ubyB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCI7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGdlaXN0U2FucyA9IEdlaXN0KHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHlcbiAgICAgICAgY2xhc3NOYW1lPXtgJHtnZWlzdFNhbnMudmFyaWFibGV9ICR7Z2Vpc3RNb25vLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZ2Vpc3RTYW5zIiwiZ2Vpc3RNb25vIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/layout.tsx */ \"(ssr)/./src/app/dashboard/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUF5SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/settings/page.tsx */ \"(ssr)/./src/app/dashboard/settings/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q1BDRCU1QyU1Q0Rlc2t0b3AlNUMlNUNkb29oJTIwYWklNUMlNUNkb29oLWFpLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3NldHRpbmdzJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNMQUFpSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcUENEXFxcXERlc2t0b3BcXFxcZG9vaCBhaVxcXFxkb29oLWFpLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxzZXR0aW5nc1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CPCD%5C%5CDesktop%5C%5Cdooh%20ai%5C%5Cdooh-ai-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Csettings%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/layout.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboard/layout.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/sidebar */ \"(ssr)/./src/components/dashboard/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-slate-50 dark:bg-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 lg:ml-64 overflow-auto\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRXdEO0FBRXpDLFNBQVNDLGdCQUFnQixFQUN0Q0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDSixrRUFBT0E7Ozs7OzBCQUNSLDhEQUFDRztnQkFBSUMsV0FBVTswQkFDWkY7Ozs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDRFxcRGVza3RvcFxcZG9vaCBhaVxcZG9vaC1haS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxkYXNoYm9hcmRcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNpZGViYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZGFzaGJvYXJkL3NpZGViYXInXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIGJnLXNsYXRlLTUwIGRhcms6Ymctc2xhdGUtOTAwXCI+XG4gICAgICA8U2lkZWJhciAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbGc6bWwtNjQgb3ZlcmZsb3ctYXV0b1wiPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNpZGViYXIiLCJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/settings/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/settings/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Database,Eye,EyeOff,Key,Lock,Mail,Monitor,Moon,Save,Settings,Shield,Smartphone,Sun,User!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction SettingsPage() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('profile');\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [darkMode, setDarkMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ar');\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: true,\n        push: true,\n        sms: false,\n        marketing: false\n    });\n    const settingsTabs = [\n        {\n            id: 'profile',\n            name: 'الملف الشخصي',\n            icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: 'notifications',\n            name: 'التنبيهات',\n            icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: 'security',\n            name: 'الأمان',\n            icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: 'billing',\n            name: 'الفوترة',\n            icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: 'integrations',\n            name: 'التكاملات',\n            icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: 'preferences',\n            name: 'التفضيلات',\n            icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    const connectedPlatforms = [\n        {\n            name: 'Google Ads',\n            status: 'connected',\n            lastSync: '2024-01-26T15:30:00Z'\n        },\n        {\n            name: 'Facebook Ads',\n            status: 'connected',\n            lastSync: '2024-01-26T14:20:00Z'\n        },\n        {\n            name: 'TikTok Ads',\n            status: 'connected',\n            lastSync: '2024-01-26T13:45:00Z'\n        },\n        {\n            name: 'Snapchat Ads',\n            status: 'disconnected',\n            lastSync: null\n        },\n        {\n            name: 'LinkedIn Ads',\n            status: 'error',\n            lastSync: '2024-01-25T10:15:00Z'\n        },\n        {\n            name: 'YouTube Ads',\n            status: 'connected',\n            lastSync: '2024-01-26T16:00:00Z'\n        }\n    ];\n    const renderProfileSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-slate-900 dark:text-white\",\n                                children: \"المعلومات الشخصية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                className: \"text-slate-600 dark:text-slate-300\",\n                                children: \"تحديث معلوماتك الشخصية وبيانات الاتصال\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                                children: \"الاسم الكامل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                defaultValue: \"أحمد محمد\",\n                                                className: \"mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                                children: \"البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                defaultValue: \"<EMAIL>\",\n                                                className: \"mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                                children: \"رقم الهاتف\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                defaultValue: \"+966501234567\",\n                                                className: \"mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                                children: \"المسمى الوظيفي\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                defaultValue: \"مدير التسويق الرقمي\",\n                                                className: \"mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                        children: \"نبذة مختصرة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        className: \"mt-1 w-full px-3 py-2 border border-slate-200 dark:border-slate-700 rounded-md bg-white dark:bg-slate-800 text-slate-900 dark:text-white\",\n                                        rows: 3,\n                                        defaultValue: \"مدير تسويق رقمي مع خبرة 5 سنوات في إدارة الحملات الإعلانية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حفظ التغييرات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n            lineNumber: 59,\n            columnNumber: 5\n        }, this);\n    const renderNotificationSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-slate-900 dark:text-white\",\n                                children: \"إعدادات التنبيهات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                className: \"text-slate-600 dark:text-slate-300\",\n                                children: \"اختر كيف ومتى تريد تلقي التنبيهات\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                            children: \"تنبيهات البريد الإلكتروني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                            children: \"تلقي التنبيهات عبر البريد الإلكتروني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: notifications.email ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setNotifications((prev)=>({\n                                                        ...prev,\n                                                        email: !prev.email\n                                                    })),\n                                            children: notifications.email ? 'مفعل' : 'معطل'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                            children: \"التنبيهات الفورية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                            children: \"تنبيهات فورية في المتصفح\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: notifications.push ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setNotifications((prev)=>({\n                                                        ...prev,\n                                                        push: !prev.push\n                                                    })),\n                                            children: notifications.push ? 'مفعل' : 'معطل'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-blue-600 dark:text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                            children: \"رسائل SMS\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                            children: \"تنبيهات عبر الرسائل النصية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: notifications.sms ? \"default\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setNotifications((prev)=>({\n                                                        ...prev,\n                                                        sms: !prev.sms\n                                                    })),\n                                            children: notifications.sms ? 'مفعل' : 'معطل'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n            lineNumber: 104,\n            columnNumber: 5\n        }, this);\n    const renderSecuritySettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-slate-900 dark:text-white\",\n                                    children: \"الأمان وكلمة المرور\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-slate-600 dark:text-slate-300\",\n                                    children: \"إدارة إعدادات الأمان وكلمة المرور\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                            children: \"كلمة المرور الحالية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    placeholder: \"أدخل كلمة المرور الحالية\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    className: \"absolute right-3 top-3 h-4 w-4 text-slate-400\",\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 33\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 46\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                            children: \"كلمة المرور الجديدة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"password\",\n                                            placeholder: \"أدخل كلمة المرور الجديدة\",\n                                            className: \"mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                            children: \"تأكيد كلمة المرور\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            type: \"password\",\n                                            placeholder: \"أعد إدخال كلمة المرور الجديدة\",\n                                            className: \"mt-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 13\n                                        }, this),\n                                        \"تحديث كلمة المرور\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-slate-900 dark:text-white\",\n                                    children: \"المصادقة الثنائية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"text-slate-600 dark:text-slate-300\",\n                                    children: \"إضافة طبقة حماية إضافية لحسابك\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-slate-900 dark:text-white\",\n                                                children: \"تفعيل المصادقة الثنائية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                children: \"استخدم تطبيق المصادقة لحماية إضافية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"تفعيل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n            lineNumber: 171,\n            columnNumber: 5\n        }, this);\n    const renderIntegrationsSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-slate-900 dark:text-white\",\n                                children: \"المنصات المتصلة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                className: \"text-slate-600 dark:text-slate-300\",\n                                children: \"إدارة اتصالاتك مع منصات الإعلانات المختلفة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: connectedPlatforms.map((platform, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white text-sm font-medium\",\n                                                        children: platform.name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-slate-900 dark:text-white\",\n                                                            children: platform.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-slate-600 dark:text-slate-300\",\n                                                            children: platform.lastSync ? `آخر مزامنة: ${new Date(platform.lastSync).toLocaleDateString('ar')}` : 'غير متصل'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    className: platform.status === 'connected' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : platform.status === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',\n                                                    children: platform.status === 'connected' ? 'متصل' : platform.status === 'error' ? 'خطأ' : 'غير متصل'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: platform.status === 'connected' ? 'قطع الاتصال' : 'اتصال'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n            lineNumber: 235,\n            columnNumber: 5\n        }, this);\n    const renderPreferencesSettings = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-slate-900 dark:text-white\",\n                                children: \"تفضيلات العرض\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                className: \"text-slate-600 dark:text-slate-300\",\n                                children: \"تخصيص مظهر وسلوك التطبيق\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                        children: \"المظهر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 mt-2\",\n                                        children: [\n                                            {\n                                                value: 'light',\n                                                label: 'فاتح',\n                                                icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n                                            },\n                                            {\n                                                value: 'dark',\n                                                label: 'مظلم',\n                                                icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n                                            },\n                                            {\n                                                value: 'system',\n                                                label: 'النظام',\n                                                icon: _barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"]\n                                            }\n                                        ].map((theme)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: darkMode === theme.value ? \"default\" : \"outline\",\n                                                size: \"sm\",\n                                                onClick: ()=>setDarkMode(theme.value),\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(theme.icon, {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    theme.label\n                                                ]\n                                            }, theme.value, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                        children: \"اللغة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: language,\n                                        onChange: (e)=>setLanguage(e.target.value),\n                                        className: \"mt-2 px-3 py-2 border border-slate-200 dark:border-slate-700 rounded-md bg-white dark:bg-slate-800 text-slate-900 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"ar\",\n                                                children: \"العربية\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"en\",\n                                                children: \"English\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"fr\",\n                                                children: \"Fran\\xe7ais\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-slate-900 dark:text-white\",\n                                        children: \"المنطقة الزمنية\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"mt-2 px-3 py-2 border border-slate-200 dark:border-slate-700 rounded-md bg-white dark:bg-slate-800 text-slate-900 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Asia/Riyadh\",\n                                                children: \"الرياض (GMT+3)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Asia/Dubai\",\n                                                children: \"دبي (GMT+4)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"Africa/Cairo\",\n                                                children: \"القاهرة (GMT+2)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"bg-blue-600 hover:bg-blue-700 text-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Database_Eye_EyeOff_Key_Lock_Mail_Monitor_Moon_Save_Settings_Shield_Smartphone_Sun_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"حفظ التفضيلات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n            lineNumber: 279,\n            columnNumber: 5\n        }, this);\n    const renderTabContent = ()=>{\n        switch(activeTab){\n            case 'profile':\n                return renderProfileSettings();\n            case 'notifications':\n                return renderNotificationSettings();\n            case 'security':\n                return renderSecuritySettings();\n            case 'integrations':\n                return renderIntegrationsSettings();\n            case 'preferences':\n                return renderPreferencesSettings();\n            default:\n                return renderProfileSettings();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"p-6 space-y-6 bg-slate-50 dark:bg-slate-900 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-slate-900 dark:text-white\",\n                            children: \"الإعدادات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-700 dark:text-slate-300\",\n                            children: \"إدارة إعدادات حسابك وتفضيلات التطبيق\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"lg:w-64 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-2\",\n                                children: settingsTabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(tab.id),\n                                        className: `w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-md transition-colors ${activeTab === tab.id ? 'bg-blue-600 text-white' : 'text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700'}`,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tab.icon, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: tab.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, tab.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 372,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: renderTabContent()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n                lineNumber: 370,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\app\\\\dashboard\\\\settings\\\\page.tsx\",\n        lineNumber: 359,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/settings/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,Brain,FileText,LayoutDashboard,LogOut,Menu,Settings,Target,Users,X,Zap!=!lucide-react */ \"(ssr)/../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n// import { useAuth } from '@/components/auth/auth-guard'\n\nconst navigation = [\n    {\n        name: 'لوحة التحكم',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        current: false\n    },\n    {\n        name: 'العملاء',\n        href: '/dashboard/clients',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الحملات',\n        href: '/dashboard/campaigns',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التحليلات',\n        href: '/dashboard/analytics',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الذكاء الاصطناعي',\n        href: '/dashboard/ai-insights',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التقارير',\n        href: '/dashboard/reports',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        current: false\n    },\n    {\n        name: 'التنبيهات',\n        href: '/dashboard/notifications',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الأتمتة',\n        href: '/dashboard/automation',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        current: false\n    }\n];\nconst adminNavigation = [\n    {\n        name: 'المستخدمون',\n        href: '/dashboard/users',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        current: false\n    },\n    {\n        name: 'الإعدادات',\n        href: '/dashboard/settings',\n        icon: _barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        current: false\n    }\n];\nfunction Sidebar({ className }) {\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // const { userProfile, signOut, isAdmin } = useAuth()\n    // Mock data for demo\n    const userProfile = {\n        name: 'مستخدم تجريبي',\n        email: '<EMAIL>'\n    };\n    const signOut = ()=>console.log('Sign out');\n    const isAdmin = true;\n    const allNavigation = isAdmin ? [\n        ...navigation,\n        ...adminNavigation\n    ] : navigation;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('lg:hidden', className),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"DOOH AI\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                children: sidebarOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 28\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 56\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-50 lg:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed inset-0 bg-black/20\",\n                                onClick: ()=>setSidebarOpen(false)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"fixed left-0 top-0 h-full w-64 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                                    navigation: allNavigation,\n                                    pathname: pathname,\n                                    userProfile: userProfile,\n                                    signOut: signOut,\n                                    onNavigate: ()=>setSidebarOpen(false)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0', className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-grow bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContent, {\n                        navigation: allNavigation,\n                        pathname: pathname,\n                        userProfile: userProfile,\n                        signOut: signOut\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction SidebarContent({ navigation, pathname, userProfile, signOut, onNavigate }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center h-16 px-4 border-b border-slate-200 dark:border-slate-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-bold text-blue-600 dark:text-blue-400\",\n                    children: \"DOOH AI Dashboard\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 px-4 py-4 space-y-2\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: item.href,\n                        onClick: onNavigate,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors', isActive ? 'bg-blue-600 text-white' : 'text-slate-700 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: \"mr-3 h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, item.name, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-slate-200 dark:border-slate-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-white\",\n                                    children: userProfile?.name?.charAt(0)?.toUpperCase() || 'U'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium truncate text-slate-900 dark:text-white\",\n                                        children: userProfile?.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-slate-600 dark:text-slate-400 truncate\",\n                                        children: userProfile?.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: signOut,\n                        className: \"w-full justify-start text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white hover:bg-slate-100 dark:hover:bg-slate-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_Brain_FileText_LayoutDashboard_LogOut_Menu_Settings_Target_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            \"تسجيل الخروج\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\dashboard\\\\sidebar.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800\",\n            secondary: \"border-transparent bg-slate-100 text-slate-800 dark:bg-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-600\",\n            destructive: \"border-transparent bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-800\",\n            outline: \"border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:text-white dark:hover:bg-blue-700\",\n            destructive: \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:text-white dark:hover:bg-red-700\",\n            outline: \"border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700\",\n            secondary: \"bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white hover:bg-slate-200 dark:hover:bg-slate-600\",\n            ghost: \"text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700\",\n            link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4Qiw4UkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQ0RcXERlc2t0b3BcXGRvb2ggYWlcXGRvb2gtYWktZGFzaGJvYXJkXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxidXR0b24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBidXR0b25WYXJpYW50cyA9IGN2YShcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLWJsdWUtNTAwIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBob3ZlcjpiZy1ibHVlLTcwMCBkYXJrOmJnLWJsdWUtNjAwIGRhcms6dGV4dC13aGl0ZSBkYXJrOmhvdmVyOmJnLWJsdWUtNzAwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctcmVkLTYwMCB0ZXh0LXdoaXRlIGhvdmVyOmJnLXJlZC03MDAgZGFyazpiZy1yZWQtNjAwIGRhcms6dGV4dC13aGl0ZSBkYXJrOmhvdmVyOmJnLXJlZC03MDBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBib3JkZXItc2xhdGUtMzAwIGRhcms6Ym9yZGVyLXNsYXRlLTYwMCBiZy13aGl0ZSBkYXJrOmJnLXNsYXRlLTgwMCB0ZXh0LXNsYXRlLTcwMCBkYXJrOnRleHQtc2xhdGUtMzAwIGhvdmVyOmJnLXNsYXRlLTUwIGRhcms6aG92ZXI6Ymctc2xhdGUtNzAwXCIsXG4gICAgICAgIHNlY29uZGFyeTpcbiAgICAgICAgICBcImJnLXNsYXRlLTEwMCBkYXJrOmJnLXNsYXRlLTcwMCB0ZXh0LXNsYXRlLTkwMCBkYXJrOnRleHQtd2hpdGUgaG92ZXI6Ymctc2xhdGUtMjAwIGRhcms6aG92ZXI6Ymctc2xhdGUtNjAwXCIsXG4gICAgICAgIGdob3N0OiBcInRleHQtc2xhdGUtNzAwIGRhcms6dGV4dC1zbGF0ZS0zMDAgaG92ZXI6Ymctc2xhdGUtMTAwIGRhcms6aG92ZXI6Ymctc2xhdGUtNzAwXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZVwiLFxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgZGVmYXVsdDogXCJoLTEwIHB4LTQgcHktMlwiLFxuICAgICAgICBzbTogXCJoLTkgcm91bmRlZC1tZCBweC0zXCIsXG4gICAgICAgIGxnOiBcImgtMTEgcm91bmRlZC1tZCBweC04XCIsXG4gICAgICAgIGljb246IFwiaC0xMCB3LTEwXCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgIHNpemU6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH1cbilcblxuZXhwb3J0IGludGVyZmFjZSBCdXR0b25Qcm9wc1xuICBleHRlbmRzIFJlYWN0LkJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50PixcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiB7XG4gIGFzQ2hpbGQ/OiBib29sZWFuXG59XG5cbmNvbnN0IEJ1dHRvbiA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTEJ1dHRvbkVsZW1lbnQsIEJ1dHRvblByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB2YXJpYW50LCBzaXplLCBhc0NoaWxkID0gZmFsc2UsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcbiAgICByZXR1cm4gKFxuICAgICAgPENvbXBcbiAgICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuQnV0dG9uLmRpc3BsYXlOYW1lID0gXCJCdXR0b25cIlxuXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJhc0NoaWxkIiwicHJvcHMiLCJyZWYiLCJDb21wIiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 text-slate-900 dark:text-white shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight text-slate-900 dark:text-white\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-slate-600 dark:text-slate-300\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 px-3 py-2 text-sm text-slate-900 dark:text-white ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-slate-500 dark:placeholder:text-slate-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\dooh ai\\\\dooh-ai-dashboard\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateCPC: () => (/* binding */ calculateCPC),\n/* harmony export */   calculateCPM: () => (/* binding */ calculateCPM),\n/* harmony export */   calculateCTR: () => (/* binding */ calculateCTR),\n/* harmony export */   calculateROAS: () => (/* binding */ calculateROAS),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = 'USD') {\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency: currency\n    }).format(amount);\n}\nfunction formatNumber(num) {\n    return new Intl.NumberFormat('en-US').format(num);\n}\nfunction formatPercentage(value) {\n    return `${(value * 100).toFixed(2)}%`;\n}\nfunction formatDate(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n}\nfunction formatDateTime(date) {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    return dateObj.toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n    });\n}\nfunction calculateCTR(clicks, impressions) {\n    if (impressions === 0) return 0;\n    return clicks / impressions * 100;\n}\nfunction calculateCPC(cost, clicks) {\n    if (clicks === 0) return 0;\n    return cost / clicks;\n}\nfunction calculateCPM(cost, impressions) {\n    if (impressions === 0) return 0;\n    return cost / impressions * 1000;\n}\nfunction calculateROAS(revenue, cost) {\n    if (cost === 0) return 0;\n    return revenue / cost;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fsettings%2Fpage&page=%2Fdashboard%2Fsettings%2Fpage&appPaths=%2Fdashboard%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fsettings%2Fpage.tsx&appDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPCD%5CDesktop%5Cdooh%20ai%5Cdooh-ai-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();